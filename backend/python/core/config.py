import os
from typing import Dict
import yaml
from pathlib import Path
from typing import List, Optional
from pydantic import BaseModel, Field, ConfigDict

class LogConfig(BaseModel):
    """日志配置"""
    dir: str = Field(description="日志目录")
    name: str = Field(description="日志文件名")
    level: str = Field(default="info", description="日志级别")
    max_size_m: int = Field(default=5, description="日志文件最大大小(MB)")


class CorsConfig(BaseModel):
    """跨域配置"""
    origins: List[str] = Field(default_factory=list, description="允许的源")
    allow_credentials: bool = Field(default=True, description="是否允许凭证")
    allow_methods: List[str] = Field(default_factory=lambda: ["*"], description="允许的方法")
    allow_headers: List[str] = Field(default_factory=lambda: ["*"], description="允许的头部")


class ApiConfig(BaseModel):
    """API配置"""
    host: str = Field(default="0.0.0.0", description="服务器主机")
    port: int = Field(default=8080, description="服务器端口")
    reload: bool = Field(default=False, description="是否启用热重载")
    cors: CorsConfig = Field(default_factory=CorsConfig, description="跨域配置")
    version: int = Field(default=1, description="API版本")

class DataConfig(BaseModel):
    """数据配置"""
    repos_path: str = Field(description="仓库目录")
    cache_dir: str = Field(description="缓存目录")
    ignored_terms_path: str = Field(description="忽略的词路径")

class LLMConfig(BaseModel):
    """大语言模型配置"""
    base_url: str = Field(description="API基础URL")
    api_key: str = Field(description="API密钥")
    model: str = Field(description="模型名称")
    temperature: float = Field(default=0.7, description="温度参数")
    max_tokens: int = Field(default=8192, description="最大token数")
    timeout: int = Field(default=60, description="请求超时时间(秒)")

class ParallelFilterConfig(BaseModel):
    """并行过滤配置"""
    enabled: bool = Field(default=True, description="是否启用并行过滤")
    min_snippets_for_parallel: int = Field(default=5, description="启用并行过滤的最小代码片段数量")
    max_workers: int = Field(default=10, description="最大并行线程数")
    progress_interval: int = Field(default=5, description="进度显示间隔")


class DeepSearchConfig(BaseModel):
    """深度搜索配置"""
    max_iterations: int = Field(default=3, description="最大循环次数")
    max_sub_queries: int = Field(default=3, description="每次循环的最大子查询数量")
    max_new_queries: int = Field(default=2, description="每次生成的新查询最大数量")
    search_types: List[str] = Field(default_factory=lambda: ["grep", "embedding"], description="支持的搜索类型")
    parallel_filter: ParallelFilterConfig = Field(default_factory=ParallelFilterConfig, description="并行过滤配置")


class FileFilterConfig(BaseModel):
    """文件过滤配置"""
    exclude: List[str] = Field(default_factory=list, description="排除的文件/目录模式")
    include: List[str] = Field(default_factory=list, description="包含的文件扩展名")
    max_file_size: int = Field(default=1048576, description="最大文件大小(字节)")  # 1MB

class ChunkConfig(BaseModel):
    """分块配置"""
    name: str = Field(default="line", description="分块策略名称")

class Config(BaseModel):
    """应用配置"""
    model_config = ConfigDict(extra="allow")

    log: LogConfig = Field(description="日志配置")
    api: ApiConfig = Field(description="API配置")
    data: DataConfig = Field(description="数据配置")
    llm: LLMConfig = Field(description="大语言模型配置")
    deepsearch: DeepSearchConfig = Field(description="深度搜索配置")
    file_filter: FileFilterConfig = Field(description="文件过滤配置")
    chunk: ChunkConfig = Field(description="分块配置")

# 全局配置实例
_config: Optional[Dict[str, Config]] = {}


def load_config(env: str) -> Config:
    """
    加载配置文件

    Args:
        env: 环境名称，默认为'dev'

    Returns:
        Config: 配置对象

    Raises:
        FileNotFoundError: 配置文件不存在
        yaml.YAMLError: YAML解析错误
        ValueError: 配置验证错误
    """
    global _config
    if _config and env in _config:
        return _config[env]

    # 获取配置文件路径
    config_dir = Path(__file__).parent.parent / "config"
    config_file = config_dir / f"config.{env}.yaml"

    # 如果环境特定的配置文件不存在，尝试使用默认配置文件
    if not config_file.exists():
        config_file = config_dir / "config.yaml"

    if not config_file.exists():
        raise FileNotFoundError(f"配置文件不存在: {config_file}")

    try:
        # 读取YAML文件
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f)

        # 验证并创建配置对象
        _config[env] = Config(**config_data)
        return _config[env]

    except yaml.YAMLError as e:
        raise yaml.YAMLError(f"YAML解析错误: {e}")
    except Exception as e:
        raise ValueError(f"配置验证错误: {e}")


def get_config(env: str = os.getenv('ENV', 'dev')) -> Config:
    """
    获取配置实例（带缓存）

    Returns:
        Config: 配置对象
    """
    return load_config(env)