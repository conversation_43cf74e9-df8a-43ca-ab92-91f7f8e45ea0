#!/usr/bin/env python3
"""
测试 trace_id 功能
"""

import sys
import os
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.trace_context import (
    TraceContextManager, 
    get_trace_id, 
    start_trace, 
    clear_trace,
    TraceAwareThreadPoolExecutor
)
from utils.trace_logger import (
    get_trace_logger, 
    trace_info, 
    trace_warning, 
    trace_error,
    log_function_entry_exit,
    log_execution_time
)


def test_basic_trace_context():
    """测试基本的 trace 上下文功能"""
    print("=== 测试基本 trace 上下文功能 ===")
    
    # 测试没有 trace_id 的情况
    logger = get_trace_logger("test")
    logger.info("没有 trace_id 的日志")
    
    # 测试设置 trace_id
    with TraceContextManager("test-trace-001", user_id=12345, action="test"):
        logger.info("设置了 trace_id 的日志")
        logger.warning("这是一个警告")
        logger.error("这是一个错误")
        
        # 验证 trace_id 获取
        current_trace_id = get_trace_id()
        print(f"当前 trace_id: {current_trace_id}")
        assert current_trace_id == "test-trace-001"
    
    # 退出上下文后应该没有 trace_id
    current_trace_id = get_trace_id()
    print(f"退出上下文后的 trace_id: '{current_trace_id}'")
    assert current_trace_id == ""
    
    print("✅ 基本 trace 上下文测试通过\n")


def test_nested_trace_context():
    """测试嵌套的 trace 上下文"""
    print("=== 测试嵌套 trace 上下文 ===")
    
    logger = get_trace_logger("test_nested")
    
    with TraceContextManager("outer-trace", level="outer"):
        logger.info("外层 trace 上下文")
        outer_trace_id = get_trace_id()
        
        with TraceContextManager("inner-trace", level="inner"):
            logger.info("内层 trace 上下文")
            inner_trace_id = get_trace_id()
            assert inner_trace_id == "inner-trace"
        
        # 回到外层
        logger.info("回到外层 trace 上下文")
        current_trace_id = get_trace_id()
        assert current_trace_id == outer_trace_id
    
    print("✅ 嵌套 trace 上下文测试通过\n")


def worker_function(worker_id: int, trace_id: str):
    """工作线程函数"""
    logger = get_trace_logger(f"worker_{worker_id}")
    
    logger.info(f"工作线程 {worker_id} 开始执行")
    time.sleep(0.1)  # 模拟工作
    
    current_trace_id = get_trace_id()
    logger.info(f"工作线程 {worker_id} 的 trace_id: {current_trace_id}")
    
    # 验证 trace_id 是否正确传递
    assert current_trace_id == trace_id, f"Expected {trace_id}, got {current_trace_id}"
    
    logger.info(f"工作线程 {worker_id} 执行完成")
    return f"worker_{worker_id}_result"


def test_thread_pool_trace_propagation():
    """测试线程池中的 trace 传播"""
    print("=== 测试线程池 trace 传播 ===")
    
    main_logger = get_trace_logger("test_thread_pool")
    
    with TraceContextManager("thread-pool-trace", operation="parallel_work"):
        main_logger.info("开始并行任务")
        
        # 使用支持 trace 的线程池
        with TraceAwareThreadPoolExecutor(max_workers=3) as executor:
            # 提交多个任务
            futures = []
            for i in range(5):
                future = executor.submit(worker_function, i, "thread-pool-trace")
                futures.append(future)
            
            # 等待所有任务完成
            results = []
            for future in as_completed(futures):
                try:
                    result = future.result()
                    results.append(result)
                    main_logger.info(f"任务完成: {result}")
                except Exception as e:
                    main_logger.error(f"任务执行失败: {e}")
        
        main_logger.info(f"所有并行任务完成，共 {len(results)} 个结果")
    
    print("✅ 线程池 trace 传播测试通过\n")


@log_function_entry_exit()
@log_execution_time()
def decorated_function(param1: str, param2: int):
    """被装饰的测试函数"""
    logger = get_trace_logger("decorated_function")
    logger.info(f"执行业务逻辑: param1={param1}, param2={param2}")
    time.sleep(0.1)  # 模拟工作
    return f"result_{param1}_{param2}"


def test_decorators():
    """测试装饰器功能"""
    print("=== 测试装饰器功能 ===")
    
    with TraceContextManager("decorator-trace", test_type="decorator"):
        result = decorated_function("test", 42)
        print(f"装饰器函数返回: {result}")
    
    print("✅ 装饰器测试通过\n")


def test_convenience_functions():
    """测试便捷函数"""
    print("=== 测试便捷函数 ===")
    
    with TraceContextManager("convenience-trace"):
        trace_info("这是便捷的 info 日志")
        trace_warning("这是便捷的 warning 日志")
        trace_error("这是便捷的 error 日志")
    
    print("✅ 便捷函数测试通过\n")


def test_manual_trace_management():
    """测试手动 trace 管理"""
    print("=== 测试手动 trace 管理 ===")
    
    logger = get_trace_logger("manual_trace")
    
    # 手动开始 trace
    trace_id = start_trace("manual-trace-001", operation="manual_test")
    logger.info("手动设置的 trace")
    
    current_trace_id = get_trace_id()
    assert current_trace_id == trace_id
    
    # 清除 trace
    clear_trace()
    current_trace_id = get_trace_id()
    assert current_trace_id == ""
    
    logger.info("清除 trace 后的日志")
    
    print("✅ 手动 trace 管理测试通过\n")


def simulate_api_request():
    """模拟 API 请求处理"""
    print("=== 模拟 API 请求处理 ===")
    
    # 模拟 API 入口
    request_trace_id = "api-request-12345"
    
    with TraceContextManager(
        request_trace_id, 
        endpoint="/api/search",
        user_id="user_001",
        ip="*************"
    ):
        api_logger = get_trace_logger("api")
        api_logger.info("API 请求开始处理")
        
        # 模拟业务逻辑调用
        service_logger = get_trace_logger("service")
        service_logger.info("调用业务服务")
        
        # 模拟数据库操作
        db_logger = get_trace_logger("database")
        db_logger.info("执行数据库查询")
        
        # 模拟并行处理
        with TraceAwareThreadPoolExecutor(max_workers=2) as executor:
            futures = [
                executor.submit(lambda: get_trace_logger("task1").info("执行任务1")),
                executor.submit(lambda: get_trace_logger("task2").info("执行任务2"))
            ]
            
            for future in as_completed(futures):
                future.result()
        
        api_logger.info("API 请求处理完成")
    
    print("✅ API 请求模拟测试通过\n")


def main():
    """主测试函数"""
    print("开始 trace_id 功能测试...\n")
    
    try:
        test_basic_trace_context()
        test_nested_trace_context()
        test_thread_pool_trace_propagation()
        test_decorators()
        test_convenience_functions()
        test_manual_trace_management()
        simulate_api_request()
        
        print("🎉 所有测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
