#!/usr/bin/env python3
"""
测试 API 中的 trace_id 功能
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.trace_context import TraceContextManager
from utils.trace_logger import get_trace_logger
from server.api.v1.api import search_code
from server.api.v1.schema import SearchRequest


def test_api_trace_functionality():
    """测试 API 中的 trace 功能"""
    print("=== 测试 API trace 功能 ===")
    
    # 模拟一个搜索请求
    request = SearchRequest(
        query="test query",
        workspace_name="test_workspace",
        search_tool="grep",
        is_stream=False,
        trace_id="api-test-trace-001"
    )
    
    # 在 trace 上下文中测试
    with TraceContextManager(
        trace_id=request.trace_id,
        test_mode=True,
        api_endpoint="/api/v1/search"
    ):
        logger = get_trace_logger(__name__)
        logger.info("开始测试 API trace 功能")
        
        try:
            # 这里我们不实际调用 API（因为需要真实的工作空间）
            # 而是测试 logger 是否正确工作
            logger.info(f"模拟处理请求: {request.query}")
            logger.warning("这是一个测试警告")
            logger.error("这是一个测试错误")
            
            print("✅ API trace 功能测试通过")
            return True
            
        except Exception as e:
            logger.error(f"API trace 功能测试失败: {e}")
            print(f"❌ API trace 功能测试失败: {e}")
            return False


def test_module_loggers():
    """测试各个模块的 logger"""
    print("\n=== 测试模块 logger ===")
    
    modules_to_test = [
        ("utils.file", "utils.file"),
        ("modules.llm.llm_client", "modules.llm.llm_client"),
        ("modules.integration.tools.search.grep_search", "modules.integration.tools.search.grep_search"),
        ("modules.integration.tools.search.term_sparse_search", "modules.integration.tools.search.term_sparse_search"),
        ("modules.term.term", "modules.term.term")
    ]
    
    success_count = 0
    
    with TraceContextManager("module-test-trace", test_type="module_logger"):
        for module_name, logger_name in modules_to_test:
            try:
                # 动态导入模块
                module = __import__(module_name, fromlist=[''])
                
                # 检查模块是否有 logger 属性
                if hasattr(module, 'logger'):
                    logger = module.logger
                    logger.info(f"测试 {module_name} 模块的 logger")
                    print(f"✅ {module_name} logger 正常")
                    success_count += 1
                else:
                    print(f"❌ {module_name} 没有 logger 属性")
                    
            except Exception as e:
                print(f"❌ {module_name} 测试失败: {e}")
    
    print(f"模块 logger 测试结果: {success_count}/{len(modules_to_test)}")
    return success_count == len(modules_to_test)


def test_trace_propagation_in_modules():
    """测试模块间的 trace 传播"""
    print("\n=== 测试模块间 trace 传播 ===")
    
    try:
        with TraceContextManager("propagation-test-trace", operation="cross_module_test"):
            # 测试 utils.file 模块
            from utils.file import should_ignore_path
            result = should_ignore_path("/test/path")
            
            # 测试 modules.llm.llm_client 模块
            from modules.llm.llm_client import LLMClient
            client = LLMClient()
            
            print("✅ 模块间 trace 传播测试通过")
            return True
            
    except Exception as e:
        print(f"❌ 模块间 trace 传播测试失败: {e}")
        return False


def test_trace_id_in_logs():
    """测试日志中是否包含 trace_id"""
    print("\n=== 测试日志中的 trace_id ===")
    
    import tempfile
    import logging
    from utils.trace_logger import TraceFormatter
    
    # 创建一个临时的日志处理器来捕获日志输出
    with tempfile.NamedTemporaryFile(mode='w+', delete=False) as temp_file:
        temp_handler = logging.FileHandler(temp_file.name)
        temp_formatter = TraceFormatter(
            fmt='%(asctime)s - %(name)s - %(levelname)s - %(trace_info)s - %(message)s'
        )
        temp_handler.setFormatter(temp_formatter)
        
        # 创建测试 logger
        test_logger = get_trace_logger("trace_test")
        test_logger.logger.addHandler(temp_handler)
        
        try:
            # 测试没有 trace_id 的情况
            test_logger.info("没有 trace_id 的日志")
            
            # 测试有 trace_id 的情况
            with TraceContextManager("log-test-trace", user_id=12345):
                test_logger.info("有 trace_id 的日志")
                test_logger.warning("有 trace_id 的警告")
            
            # 读取日志文件内容
            temp_handler.close()
            with open(temp_file.name, 'r') as f:
                log_content = f.read()
            
            print("日志内容:")
            print(log_content)
            
            # 检查是否包含 trace_id
            if "trace_id=log-test-trace" in log_content:
                print("✅ 日志中包含 trace_id")
                return True
            else:
                print("❌ 日志中不包含 trace_id")
                return False
                
        finally:
            # 清理
            test_logger.logger.removeHandler(temp_handler)
            os.unlink(temp_file.name)


def main():
    """主测试函数"""
    print("开始测试 trace_id 在 API 中的功能...\n")
    
    tests = [
        ("API trace 功能", test_api_trace_functionality),
        ("模块 logger", test_module_loggers),
        ("模块间 trace 传播", test_trace_propagation_in_modules),
        ("日志中的 trace_id", test_trace_id_in_logs),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"运行测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
    
    print("="*50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！trace_id 功能正常工作！")
        return 0
    else:
        print("⚠️  部分测试失败，需要检查 trace_id 功能")
        return 1


if __name__ == "__main__":
    exit(main())
