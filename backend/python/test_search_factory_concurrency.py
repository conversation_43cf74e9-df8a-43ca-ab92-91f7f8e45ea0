#!/usr/bin/env python3
"""
测试搜索工具工厂的并发安全性
"""

import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from modules.integration.tools.search.search_factory import get_search_tool, clear_instance_pool, get_pool_status
from modules.integration.tools.search.search_tool import Search<PERSON><PERSON><PERSON><PERSON>


def test_concurrent_access():
    """测试并发访问同一个搜索工具实例"""
    
    # 清空实例池
    clear_instance_pool()
    
    repo_path = "/tmp/test_repo"
    search_type = SearchToolEnum.GREP
    
    results = []
    
    def get_instance(thread_id):
        """获取搜索工具实例"""
        try:
            instance = get_search_tool(search_type, repo_path)
            return {
                'thread_id': thread_id,
                'instance_id': id(instance),
                'success': True,
                'error': None
            }
        except Exception as e:
            return {
                'thread_id': thread_id,
                'instance_id': None,
                'success': False,
                'error': str(e)
            }
    
    # 使用多线程并发获取实例
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(get_instance, i) for i in range(20)]
        
        for future in as_completed(futures):
            results.append(future.result())
    
    # 分析结果
    successful_results = [r for r in results if r['success']]
    failed_results = [r for r in results if not r['success']]
    
    print(f"成功获取实例: {len(successful_results)}")
    print(f"获取失败: {len(failed_results)}")
    
    if failed_results:
        print("失败详情:")
        for result in failed_results:
            print(f"  线程 {result['thread_id']}: {result['error']}")
    
    # 检查是否所有成功的结果都返回了相同的实例
    if successful_results:
        instance_ids = set(r['instance_id'] for r in successful_results)
        print(f"不同的实例ID数量: {len(instance_ids)}")
        print(f"实例ID: {instance_ids}")
        
        if len(instance_ids) == 1:
            print("✅ 并发安全测试通过：所有线程获取到了相同的实例")
        else:
            print("❌ 并发安全测试失败：创建了多个实例")
    
    # 显示实例池状态
    pool_status = get_pool_status()
    print(f"实例池状态: {pool_status}")


def test_different_parameters():
    """测试不同参数组合的实例隔离"""
    
    clear_instance_pool()
    
    # 测试不同的参数组合
    test_cases = [
        (SearchToolEnum.GREP, "/repo1", {}),
        (SearchToolEnum.GREP, "/repo2", {}),
        (SearchToolEnum.INVERTED_INDEX, "/repo1", {}),
        (SearchToolEnum.INVERTED_INDEX, "/repo1", {"refresh": True}),
        (SearchToolEnum.INVERTED_INDEX, "/repo1", {"refresh": False}),
    ]
    
    instances = {}
    
    for i, (search_type, repo_path, kwargs) in enumerate(test_cases):
        try:
            instance = get_search_tool(search_type, repo_path, **kwargs)
            instances[i] = {
                'params': (search_type, repo_path, kwargs),
                'instance_id': id(instance),
                'success': True
            }
        except Exception as e:
            instances[i] = {
                'params': (search_type, repo_path, kwargs),
                'instance_id': None,
                'success': False,
                'error': str(e)
            }
    
    print("\n参数隔离测试:")
    for i, result in instances.items():
        if result['success']:
            print(f"  案例 {i}: {result['params']} -> 实例ID: {result['instance_id']}")
        else:
            print(f"  案例 {i}: {result['params']} -> 失败: {result.get('error', 'Unknown')}")
    
    # 检查相同参数是否返回相同实例
    print("\n重复获取测试:")
    for i, (search_type, repo_path, kwargs) in enumerate(test_cases):
        if instances[i]['success']:
            try:
                instance2 = get_search_tool(search_type, repo_path, **kwargs)
                if id(instance2) == instances[i]['instance_id']:
                    print(f"  案例 {i}: ✅ 相同参数返回相同实例")
                else:
                    print(f"  案例 {i}: ❌ 相同参数返回不同实例")
            except Exception as e:
                print(f"  案例 {i}: ❌ 重复获取失败: {e}")
    
    pool_status = get_pool_status()
    print(f"\n最终实例池状态: {pool_status}")


if __name__ == "__main__":
    print("开始并发安全测试...")
    test_concurrent_access()
    
    print("\n" + "="*50)
    test_different_parameters()
    
    print("\n" + "="*50)
    print("测试完成")
