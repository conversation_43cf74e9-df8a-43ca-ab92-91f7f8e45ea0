#!/usr/bin/env python3
"""
测试元信息提取功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.deepsearch.deep_search import DeepSearch
from modules.integration.tools.search.search_tool import SearchToolEnum


def test_extract_structure():
    """测试元信息提取功能"""
    
    # 创建DeepSearch实例
    deep_search = DeepSearch("/tmp/test_repo")
    
    # 测试用例1: 包含grep工具的响应
    test_response_1 = """
    <output tool="grep">
    <newquery>Repository</newquery>
    <newquery>Storage</newquery>
    <newquery>Persist</newquery>
    </output>
    """
    
    print("测试用例1: grep工具")
    try:
        search_tool, queries = deep_search._extract_structure(test_response_1, "newquery")
        print(f"  搜索工具: {search_tool}")
        print(f"  查询列表: {queries}")
        assert search_tool == SearchToolEnum.GREP
        assert len(queries) == 3
        assert "Repository" in queries
        print("  ✅ 测试通过")
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
    
    # 测试用例2: 包含term_sparse工具的响应
    test_response_2 = """
    <output tool="term_sparse">
    <newquery>
    CLASS Connection:
        METHOD __init__(host, user, password):
            INIT connection parameters
    </newquery>
    <newquery>
    CLASS Cursor:
        METHOD execute(query, args):
            EXECUTE SQL query
    </newquery>
    </output>
    """
    
    print("\n测试用例2: term_sparse工具")
    try:
        search_tool, queries = deep_search._extract_structure(test_response_2, "newquery")
        print(f"  搜索工具: {search_tool}")
        print(f"  查询数量: {len(queries)}")
        assert search_tool == SearchToolEnum.TERM_SPRSE
        assert len(queries) == 2
        print("  ✅ 测试通过")
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
    
    # 测试用例3: 包含inverted_index工具的响应
    test_response_3 = """
    <output tool="inverted_index">
    <newquery>Connection class methods</newquery>
    <newquery>Cursor class properties</newquery>
    </output>
    """
    
    print("\n测试用例3: inverted_index工具")
    try:
        search_tool, queries = deep_search._extract_structure(test_response_3, "newquery")
        print(f"  搜索工具: {search_tool}")
        print(f"  查询列表: {queries}")
        assert search_tool == SearchToolEnum.INVERTED_INDEX
        assert len(queries) == 2
        print("  ✅ 测试通过")
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
    
    # 测试用例4: 没有工具信息的响应
    test_response_4 = """
    <newquery>Repository</newquery>
    <newquery>Storage</newquery>
    """
    
    print("\n测试用例4: 没有工具信息")
    try:
        search_tool, queries = deep_search._extract_structure(test_response_4, "newquery")
        print(f"  搜索工具: {search_tool}")
        print(f"  查询列表: {queries}")
        assert search_tool == SearchToolEnum.ANY  # 默认值
        assert len(queries) == 2
        print("  ✅ 测试通过")
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
    
    # 测试用例5: 未知工具类型
    test_response_5 = """
    <output tool="unknown_tool">
    <newquery>test query</newquery>
    </output>
    """
    
    print("\n测试用例5: 未知工具类型")
    try:
        search_tool, queries = deep_search._extract_structure(test_response_5, "newquery")
        print(f"  搜索工具: {search_tool}")
        print(f"  查询列表: {queries}")
        assert search_tool == SearchToolEnum.ANY  # 默认值
        assert len(queries) == 1
        print("  ✅ 测试通过")
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
    
    # 测试用例6: 空结构化内容
    test_response_6 = """
    <output tool="grep">
    </output>
    """
    
    print("\n测试用例6: 空结构化内容")
    try:
        search_tool, queries = deep_search._extract_structure(test_response_6, "newquery")
        print(f"  搜索工具: {search_tool}")
        print(f"  查询列表: {queries}")
        assert search_tool == SearchToolEnum.GREP
        assert len(queries) == 0
        print("  ✅ 测试通过")
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")


def test_error_cases():
    """测试错误情况"""
    
    deep_search = DeepSearch("/tmp/test_repo")
    
    print("\n错误情况测试:")
    
    # 测试空的结构化关键字
    print("  测试空的结构化关键字")
    try:
        deep_search._extract_structure("test", "")
        print("  ❌ 应该抛出ValueError")
    except ValueError as e:
        print(f"  ✅ 正确抛出ValueError: {e}")
    except Exception as e:
        print(f"  ❌ 抛出了错误的异常类型: {e}")
    
    # 测试None的结构化关键字
    print("  测试None的结构化关键字")
    try:
        deep_search._extract_structure("test", None)
        print("  ❌ 应该抛出ValueError")
    except ValueError as e:
        print(f"  ✅ 正确抛出ValueError: {e}")
    except Exception as e:
        print(f"  ❌ 抛出了错误的异常类型: {e}")


if __name__ == "__main__":
    print("开始测试元信息提取功能...")
    test_extract_structure()
    test_error_cases()
    print("\n测试完成!")
