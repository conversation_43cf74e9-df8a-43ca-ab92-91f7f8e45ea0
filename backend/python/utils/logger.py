import logging
import os
from pathlib import Path
from logging.handlers import RotatingFileHandler
from rich.logging import RichHandler
from logging import Formatter

from core.config import get_config

log_config = get_config().log

# 项目默认日志管理器
logger = logging.getLogger(__name__)

# 根据配置文件设置日志级别
log_level_map = {
    'debug': logging.DEBUG,
    'info': logging.INFO,
    'warning': logging.WARNING,
    'error': logging.ERROR,
    'critical': logging.CRITICAL
}
log_level = log_level_map.get(log_config.level.lower(), logging.INFO)
logger.setLevel(log_level)

# 创建自定义格式器
formatter = logging.Formatter(
    fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 确保日志目录存在
log_dir = Path(log_config.dir)
log_dir.mkdir(parents=True, exist_ok=True)

# 创建文件处理器 - 使用RotatingFileHandler实现自动切分
log_file_path = log_dir / log_config.name
max_bytes = log_config.max_size_m * 1024 * 1024  # 转换为字节
backup_count = 5  # 保留5个备份文件

file_handler = RotatingFileHandler(
    filename=str(log_file_path),
    maxBytes=max_bytes,
    backupCount=backup_count,
    encoding="utf-8"
)
file_handler.setFormatter(formatter)
file_handler.setLevel(log_level)

logger.addHandler(file_handler)

# 创建控制台处理器
console_handler = RichHandler(markup=True, rich_tracebacks=True)
console_handler.setFormatter(logging.Formatter('%(message)s'))
console_handler.setLevel(log_level)

# 添加控制台处理器到logger
logger.addHandler(console_handler)

# 防止重复日志（不传播到根logger）
logger.propagate = False

# 为了兼容性，也设置根logger的基本配置（但级别设为WARNING以减少第三方库日志）
logging.basicConfig(
    level=logging.WARNING,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[file_handler]
)

# 导出一些便捷的日志函数
def get_logger(name: str = None) -> logging.Logger:
    """
    获取日志记录器

    Args:
        name: 日志记录器名称，默认使用调用模块的名称

    Returns:
        logging.Logger: 配置好的日志记录器
    """
    if name is None:
        import inspect
        frame = inspect.currentframe().f_back
        name = frame.f_globals.get('__name__', 'unknown')

    logger_instance = logging.getLogger(name)
    logger_instance.setLevel(log_level)

    # 如果logger还没有处理器，添加我们的处理器
    if not logger_instance.handlers:
        logger_instance.addHandler(file_handler)
        logger_instance.addHandler(console_handler)
        logger_instance.propagate = False

    return logger_instance

# 记录日志系统初始化信息
logger.info(f"日志系统初始化完成 - 级别: {log_config.level}, 文件: {log_file_path}, 最大大小: {log_config.max_size_m}MB")