from typing import List
from modules.integration.tools.grep_search import GrepSearchTool
from modules.integration.tools.keywords_search import KeywordsSearchTool
from modules.integration.tools.term_sparse_search import TermParseSearch
from backend.python.modules.integration.tools.search.search_tool import CodeSnippet, SearchToolABC
from modules.common.constant import SearchToolEnum

class AnySearch(SearchToolABC):
    def __init__(self, repo_path: str):
        self.repo_path = repo_path
    
    def search(self, query: str, search_type: SearchToolEnum) -> List[CodeSnippet]:
        if search_type == SearchToolEnum.GREP:
            return GrepSearchTool(self.repo_path).search(query)
        elif search_type == SearchToolEnum.KEYWORDS:
            return KeywordsSearchTool(self.repo_path).search(query)
        elif search_type == SearchToolEnum.TERM_SPRSE:
            return TermParseSearch(self.repo_path).search(query)
        else:
            raise ValueError("未知搜索工具")
