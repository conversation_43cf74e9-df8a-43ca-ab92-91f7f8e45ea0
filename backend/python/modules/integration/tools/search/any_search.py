from typing import List
from modules.integration.tools.search.grep_search import GrepSearchTool
from modules.integration.tools.search.keywords_search import InvertedIndexSearchTool
from modules.integration.tools.search.term_sparse_search import TermParseSearch
from modules.integration.tools.search.search_tool import CodeSnippet, SearchToolABC
from modules.integration.tools.search.search_tool import SearchToolEnum
from modules.integration.tools.search.search_factory import get_search_tool

class AnySearch(SearchToolABC):
    def __init__(self, repo_path: str):
        self.repo_path = repo_path

    def search(self, query: str, search_tool: SearchToolEnum, **kwargs) -> List[CodeSnippet]:
        if search_tool == SearchToolEnum.GREP:
            return get_search_tool(SearchToolEnum.GREP, self.repo_path, **kwargs).search(query)
        elif search_tool == SearchToolEnum.INVERTED_INDEX:
            return get_search_tool(SearchToolEnum.INVERTED_INDEX, self.repo_path, **kwargs).search(query)
        elif search_tool == SearchToolEnum.TERM_SPRSE:
            return get_search_tool(SearchToolEnum.TERM_SPRSE, self.repo_path, **kwargs).search(query)
        else:
            raise ValueError("未知搜索工具")
