
import hashlib
import json
import threading
from typing import Dict

from modules.integration.tools.search.search_tool import SearchToolEnum
from modules.integration.tools.search.search_tool import SearchToolABC

# 实例池，用于缓存搜索工具实例
INSTANCE_POOL: Dict[str, SearchToolABC] = {}

# 线程锁，确保实例池操作的并发安全
_POOL_LOCK = threading.RLock()

# 正在创建中的实例键，防止重复创建
_CREATING_INSTANCES: Dict[str, threading.Event] = {}

def _generate_instance_key(search_type: SearchToolEnum, repo_path: str, **kwargs) -> str:
    """
    根据入参生成hash值，包括kwargs

    Args:
        search_type: 搜索工具类型
        repo_path: 仓库路径
        **kwargs: 其他参数

    Returns:
        str: 生成的hash键值
    """
    # 创建包含所有参数的字典
    params = {
        'search_type': search_type.value,
        'repo_path': repo_path,
        **kwargs
    }

    # 将参数字典转换为排序后的JSON字符串，确保一致性
    params_str = json.dumps(params, sort_keys=True, ensure_ascii=False)

    # 生成SHA256哈希值
    return hashlib.sha256(params_str.encode('utf-8')).hexdigest()

def get_search_tool(search_type: SearchToolEnum, repo_path: str, **kwargs) -> SearchToolABC:
    """
    获取搜索工具实例，支持实例池缓存，并发安全

    Args:
        search_type: 搜索工具类型
        repo_path: 仓库路径
        **kwargs: 其他参数

    Returns:
        SearchToolABC: 搜索工具实例
    """
    # 根据入参生成hash值，包括kwargs
    instance_key = _generate_instance_key(search_type, repo_path, **kwargs)

    # 第一次检查：快速路径，如果实例已存在直接返回
    with _POOL_LOCK:
        if instance_key in INSTANCE_POOL:
            return INSTANCE_POOL[instance_key]

        # 检查是否有其他线程正在创建相同的实例
        if instance_key in _CREATING_INSTANCES:
            # 等待其他线程完成创建
            creation_event = _CREATING_INSTANCES[instance_key]
        else:
            # 标记当前线程正在创建此实例
            creation_event = threading.Event()
            _CREATING_INSTANCES[instance_key] = creation_event
            creation_event = None  # 当前线程负责创建

    # 如果有其他线程正在创建，等待完成
    if creation_event is not None:
        creation_event.wait()  # 等待其他线程完成创建
        # 其他线程创建完成后，实例应该已经在池中
        with _POOL_LOCK:
            if instance_key in INSTANCE_POOL:
                return INSTANCE_POOL[instance_key]
            else:
                # 如果其他线程创建失败，当前线程重新尝试创建
                creation_event = threading.Event()
                _CREATING_INSTANCES[instance_key] = creation_event
                creation_event = None

    # 当前线程负责创建实例
    try:
        # 在锁中完成所有操作，确保原子性
        with _POOL_LOCK:
            # 再次检查，防止在等待锁的过程中其他线程已经创建了实例
            if instance_key in INSTANCE_POOL:
                # 清理创建状态并返回已存在的实例
                if instance_key in _CREATING_INSTANCES:
                    _CREATING_INSTANCES[instance_key].set()
                    del _CREATING_INSTANCES[instance_key]
                return INSTANCE_POOL[instance_key]

            # 创建新实例（在锁中执行，确保原子性）
            instance = _create_search_tool_instance(search_type, repo_path, **kwargs)

            # 将实例添加到池中
            INSTANCE_POOL[instance_key] = instance

            # 通知等待的线程创建完成
            if instance_key in _CREATING_INSTANCES:
                _CREATING_INSTANCES[instance_key].set()
                del _CREATING_INSTANCES[instance_key]

            return instance

    except Exception as e:
        # 创建失败，通知等待的线程并清理状态
        with _POOL_LOCK:
            if instance_key in _CREATING_INSTANCES:
                _CREATING_INSTANCES[instance_key].set()
                del _CREATING_INSTANCES[instance_key]
        raise e


def _create_search_tool_instance(search_type: SearchToolEnum, repo_path: str, **kwargs) -> SearchToolABC:
    """
    创建搜索工具实例的内部方法

    Args:
        search_type: 搜索工具类型
        repo_path: 仓库路径
        **kwargs: 其他参数

    Returns:
        SearchToolABC: 搜索工具实例
    """
    if search_type == SearchToolEnum.GREP:
        from modules.integration.tools.search.grep_search import GrepSearchTool
        return GrepSearchTool(repo_path)

    elif search_type == SearchToolEnum.EMBEDDING:
        raise NotImplementedError("Embedding搜索暂未实现")
        # from modules.integration.embedding import EmbeddingSearchTool
        # return EmbeddingSearchTool(repo_path, **kwargs)

    elif search_type == SearchToolEnum.TERM_SPRSE:
        from modules.integration.tools.search.term_sparse_search import TermParseSearch
        return TermParseSearch(repo_path, **kwargs)

    elif search_type == SearchToolEnum.INVERTED_INDEX:
        from modules.integration.tools.search.keywords_search import KeywordsSearchTool
        return KeywordsSearchTool(repo_path, **kwargs)

    elif search_type == SearchToolEnum.ANY:
        from modules.integration.tools.search.any_search import AnySearch
        return AnySearch(repo_path, **kwargs)
    else:
        raise ValueError("未知搜索工具")


def clear_instance_pool() -> int:
    """
    清空实例池，释放所有缓存的搜索工具实例

    Returns:
        int: 清理的实例数量
    """
    with _POOL_LOCK:
        count = len(INSTANCE_POOL)
        INSTANCE_POOL.clear()
        _CREATING_INSTANCES.clear()
        return count


def get_pool_status() -> Dict[str, int]:
    """
    获取实例池状态信息

    Returns:
        Dict[str, int]: 包含实例池大小和正在创建实例数量的字典
    """
    with _POOL_LOCK:
        return {
            'cached_instances': len(INSTANCE_POOL),
            'creating_instances': len(_CREATING_INSTANCES)
        }
