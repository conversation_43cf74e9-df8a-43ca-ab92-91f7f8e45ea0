
import hashlib
import json
from typing import Dict, Any

from modules.integration.tools.search.search_tool import SearchToolEnum
from modules.integration.tools.search.search_tool import SearchToolABC

# 实例池，用于缓存搜索工具实例
INSTANCE_POOL: Dict[str, SearchToolABC] = {}

def _generate_instance_key(search_type: SearchToolEnum, repo_path: str, **kwargs) -> str:
    """
    根据入参生成hash值，包括kwargs

    Args:
        search_type: 搜索工具类型
        repo_path: 仓库路径
        **kwargs: 其他参数

    Returns:
        str: 生成的hash键值
    """
    # 创建包含所有参数的字典
    params = {
        'search_type': search_type.value,
        'repo_path': repo_path,
        **kwargs
    }

    # 将参数字典转换为排序后的JSON字符串，确保一致性
    params_str = json.dumps(params, sort_keys=True, ensure_ascii=False)

    # 生成SHA256哈希值
    return hashlib.sha256(params_str.encode('utf-8')).hexdigest()

def get_search_tool(search_type: SearchToolEnum, repo_path: str, **kwargs) -> SearchToolABC:
    """
    获取搜索工具实例，支持实例池缓存

    Args:
        search_type: 搜索工具类型
        repo_path: 仓库路径
        **kwargs: 其他参数

    Returns:
        SearchToolABC: 搜索工具实例
    """
    # 根据入参生成hash值，包括kwargs
    instance_key = _generate_instance_key(search_type, repo_path, **kwargs)

    # 从池中获取实例
    if instance_key in INSTANCE_POOL:
        return INSTANCE_POOL[instance_key]

    # 每个实例是独占的，需要创建新实例
    instance = None

    if search_type == SearchToolEnum.GREP:
        from modules.integration.tools.search.grep_search import GrepSearchTool
        instance = GrepSearchTool(repo_path)

    elif search_type == SearchToolEnum.EMBEDDING:
        raise NotImplementedError("Embedding搜索暂未实现")
        # from modules.integration.embedding import EmbeddingSearchTool
        # instance = EmbeddingSearchTool(repo_path, **kwargs)

    elif search_type == SearchToolEnum.TERM_SPRSE:
        from modules.integration.tools.search.term_sparse_search import TermParseSearch
        instance = TermParseSearch(repo_path, **kwargs)

    elif search_type == SearchToolEnum.KEYWORDS:
        from modules.integration.tools.search.keywords_search import KeywordsSearchTool
        instance = KeywordsSearchTool(repo_path, **kwargs)

    elif search_type == SearchToolEnum.ANY:
        from modules.integration.tools.search.any_search import AnySearch
        instance = AnySearch(repo_path, **kwargs)
    else:
        raise ValueError("未知搜索工具")

    # 将实例添加到池中
    INSTANCE_POOL[instance_key] = instance

    return instance
