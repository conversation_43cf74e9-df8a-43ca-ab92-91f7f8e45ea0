
from modules.integration.tools.search.search_tool import SearchToolEnum
from modules.integration.tools.search.search_tool import SearchToolABC

INSTANCE_POOL = {

}

def get_search_tool(search_type: SearchToolEnum, repo_path: str, **kwargs) -> SearchToolABC:
    if search_type == SearchToolEnum.GREP:
        from modules.integration.tools.search.grep_search import GrepSearchTool
        return GrepSearchTool()

    elif search_type == SearchToolEnum.EMBEDDING:
        raise NotImplementedError("Embedding搜索暂未实现")
        # from modules.integration.embedding import EmbeddingSearchTool
        # return EmbeddingSearchTool

    elif search_type == SearchToolEnum.TERM_SPRSE:
        from modules.integration.tools.search.term_sparse_search import TermParseSearch
        return TermParseSearch
    
    elif search_type == SearchToolEnum.KEYWORDS:
        from modules.integration.tools.search.keywords_search import KeywordsSearchTool
        return KeywordsSearchTool
    
    else:
        raise ValueError("未知搜索工具")
