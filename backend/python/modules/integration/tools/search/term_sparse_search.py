import logging
import json
from typing import List
from collections import Counter

from core.config import get_config
from modules.common.schema import CodeSnippet
from modules.integration.tools.search.search_tool import SearchToolABC
from modules.chunks.chunk_factory import getChunkService
from modules.term.term import get_terms, save_bm25_chunk_terms

from utils.file import FileType, generate_file_hash_name
from utils.trace_logger import get_trace_logger

# 获取支持 trace 的 logger
logger = get_trace_logger(__name__)


class TermParseSearch(SearchToolABC):
    def __init__(self, repo_path: str, cache_dir: str = get_config().data.cache_dir, refresh: bool = False):
        self.repo_path = repo_path

        self.k1 = 1.2
        self.k2 = 2.0 # Query中词频的重要程度
        self.b = 0.75

        self.chunk_content = None
        self.term_idf = None
        self.chunks_term_freqs = None
        self.avg_chunk_length = None

        # 加载缓存文件  
        self.term_sparse_file_path = save_bm25_chunk_terms(project_dir = repo_path, 
                                                           cache_dir = cache_dir, 
                                                           project_id = generate_file_hash_name(repo_path), 
                                                           refresh=refresh,
                                                           chunk_splitter=getChunkService(get_config().chunk.name)(),
                                                           k1=self.k1,
                                                           b=self.b
                                                          )
        # 读取缓存文件
        with open(self.term_sparse_file_path, 'r', encoding='utf-8') as f:
            cache_content =  json.load(f)
            self.chunk_content = cache_content['chunk_content']
            self.term_idf = cache_content['term_idf']
            self.chunks_term_freqs = cache_content['chunk_term_freqs']
            self.avg_chunk_length = cache_content['avg_chunk_length']


    def search(self, query: str, top_k: int = 20, file_type=FileType.CODE, **kwargs) -> List[CodeSnippet]:
        if not self.chunk_content or not self.term_idf or not self.chunks_term_freqs:
            return {}

        # 提取查询词
        query_terms = get_terms(query, file_type=file_type)
        if logger.isEnabledFor(logging.DEBUG):
            term_bm25 = {}
            for term, term_count in Counter(query_terms).items():
                if self.term_idf.get(term, 0) == 0:
                    continue
                term_tf = (term_count * (self.k2 + 1.0)) / (term_count + self.k2)
                term_bm25[term] = term_tf * self.term_idf[term]
            # 按排序展示
            term_bm25 = dict(sorted(term_bm25.items(), key=lambda item: item[1], reverse=True))
            # logger.debug(f"Query Terms BM25: {json.dumps(term_bm25, ensure_ascii=False, indent=2)}")

        if not query_terms:
            return {}
        
        # 计算每个文档的匹配分数
        scores = {}
        for chunk_path in self.chunk_content:
            score = self._calculate_term_sparse_score(query_terms, chunk_path)
            if score > 0:
                scores[chunk_path] = score


        # 按分数排序并返回top_k结果
        sorted_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)
        return_count = min(top_k, len(sorted_scores))

        # logger.debug(f"BM25 Search Sorted Scores: {json.dumps(sorted_scores[:return_count], ensure_ascii=False, indent=2)}")
        
        # 返回结果
        return [CodeSnippet(
            file_path=chunk_path.split(":")[0],
            start_line=int(chunk_path.split(":")[1].split("-")[0]),
            end_line=int(chunk_path.split(":")[1].split("-")[1]),
            content=self.chunk_content[chunk_path],
            context_before="",
            context_after="",
            score=scores[chunk_path]
        ) for chunk_path, _ in sorted_scores[:return_count]]

    def _calculate_term_sparse_score(self, query_terms: List[str], chunk_path: str) -> float:
        """
        计算单个文档的BM25分数
        """
        if chunk_path not in self.chunks_term_freqs:
            return 0.0
        
        score = 0.0

        for term, term_count in Counter(query_terms).items():
            if term in self.chunks_term_freqs[chunk_path] and term in self.term_idf:
                # logger.info(f"Term: {term}, Chunk: {chunk_path}, TF: {term_freqs[term]}, IDF: {self.term_idf[term]}")
                # 词频
                tf = (term_count * (self.k2 + 1.0)) / (term_count + self.k2)
                
                # IDF值
                idf = self.term_idf[term]

                # BM25公式
                score += idf * idf * tf * self.chunks_term_freqs[chunk_path][term]

        return score