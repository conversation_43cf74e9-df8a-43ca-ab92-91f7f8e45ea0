from typing import List
import json
from collections import defaultdict
from core.config import get_config
from modules.integration.tools.search.search_tool import SearchToolABC
from modules.common.schema import CodeSnippet
from utils.file import generate_file_hash_name
from modules.term.term import get_terms, save_inverted_index, FileType


class InvertedIndexSearchTool(SearchToolABC):
    def __init__(self, repo_path: str, cache_dir: str = get_config().data.cache_dir, refresh: bool = True, chunk_keywords_num: int = 20):
        self.repo_path = repo_path

        # 加载缓存文件  
        self.term_sparse_file_path = save_inverted_index(project_dir=repo_path, 
                                                           cache_dir=cache_dir, 
                                                           project_id=generate_file_hash_name(repo_path), 
                                                           refresh=refresh,
                                                           chunk_keywords_num=chunk_keywords_num
                                                          )
        
        # 加载缓存文件
        with open(self.term_sparse_file_path, 'r', encoding='utf-8') as f:
            keywords_cache = json.load(f)
            self.keywords2chunk = keywords_cache['keywords2chunk']
            self.chunk_path2idx = keywords_cache['chunk_path2idx']
            self.chunk_content = keywords_cache['chunk_content']
            
    def search(self, query: str, top_k: int = 20, file_type=FileType.CODE, **kwargs) -> List[CodeSnippet]:
        # 计算当前query的所有关键词
        query_terms = get_terms(query, file_type=file_type)

        # 获取所有关键词对应的chunk
        chunk_scores = defaultdict(int)
        for term in query_terms:
            if term in self.keywords2chunk:
                for chunk_idx in self.keywords2chunk[term]:
                    chunk_scores[self.chunk_path2idx[chunk_idx]] += 1
        
        # 按照chunk进行排序
        sorted_chunks = sorted(chunk_scores.items(), key=lambda x: x[1], reverse=True)[:min(top_k, len(chunk_scores))]

        return [
            CodeSnippet(
                file_path=chunk_path.split(":")[0],
                start_line=int(chunk_path.split(":")[1].split("-")[0]),
                end_line=int(chunk_path.split(":")[1].split("-")[1]),
                content=self.chunk_content[chunk_path],
                context_before="",
                context_after="",
                score=score
            )
            for chunk_path, score in sorted_chunks
        ]
