from typing import List
from enum import Enum
from abc import ABC, abstractmethod

from modules.common.schema import CodeSnippet
from modules.common.constant import NEW_QUERY_TAG


class SearchToolABC(ABC):
    """搜索引擎基类"""
    
    def __init__(self, repo_path: str, **args):
        pass
    
    @abstractmethod
    def search(self, query: str, **kwargs) -> List[CodeSnippet]:
        raise NotImplementedError("子类必须实现search方法")

CODE_SNIPPET_EXAMPLES = f"""
<{NEW_QUERY_TAG}>
CLASS Connection:
    PROPERTIES:
        host, port, user, password, database
        charset, autocommit, connect_timeout
        _sock, _rfile, _wfile, _next_seq_id
        _closed, _connect_kwargs

    METHOD __init__(host=None, user=None, password="", database=None, ...):
        INIT connection parameters
        SET defaults for unprovided arguments
        STORE connection kwargs
        SET _closed = True

    METHOD connect(sock=None):
        SET _closed = False
        TRY:
            ESTABLISH socket connection (UNIX or TCP)
            PERFORM socket optimizations
            INITIALIZE I/O streams
            PERFORM MySQL handshake
            AUTHENTICATE with server
            CONFIGURE connection settings
        CATCH:
            HANDLE errors appropriately

    METHOD close():
        IF not _closed:
            CLOSE socket and file objects
            SET _closed = True

    METHOD cursor(cursor_type=None):
        RETURN NEW Cursor(self)

    METHOD commit():
        SEND "COMMIT" command

    METHOD rollback():
        SEND "ROLLBACK" command

    METHOD select_db(db):
        SEND "USE db" command

    METHOD _execute_command(command, sql, params):
        PREPARE and SEND command to server
        HANDLE sequence numbering

    METHOD _read_query_result(unbuffered=False):
        READ and PROCESS server response
        RETURN result object
</{NEW_QUERY_TAG}>
<{NEW_QUERY_TAG}>
CLASS Cursor:
    PROPERTIES:
        connection, description, rowcount
        lastrowid, _rows, _result

    METHOD __init__(connection):
        STORE connection reference
        INIT result tracking variables
        SET arraysize = 1

    METHOD close():
        CLEAR internal state
        SET connection = None

    METHOD execute(query, args=None):
        IF connection is closed:
            RAISE Error
        CLEAR previous results
        IF args provided:
            ESCAPE and FORMAT parameters
        SEND query through connection
        STORE result metadata
        RETURN rowcount

    METHOD executemany(query, args_seq):
        FOR EACH args in args_seq:
            execute(query, args)
        OPTIMIZE with batch operations if possible

    METHOD fetchone():
        IF no result set:
            RAISE Error
        IF all rows fetched:
            RETURN None
        RETURN next row

    METHOD fetchmany(size=None):
        IF size not specified:
            USE arraysize
        RETURN next 'size' rows

    METHOD fetchall():
        RETURN all remaining rows

    METHOD callproc(procname, args=()):
        PREPARE CALL statement
        execute procedure call
        RETURN modified args

    METHOD _do_get_result():
        READ result from connection
        SET description, rowcount, etc.

    METHOD _escape_args(args):
        ESCAPE parameters according to type
        RETURN formatted arguments
</{NEW_QUERY_TAG}>
"""

class SearchToolEnum(Enum):
    GREP = "grep"
    EMBEDDING = "embedding"
    INVERTED_INDEX = "inverted_index"
    TERM_SPRSE = "term_sparse"
    ANY = "any"
    
    @property
    def description(self):
        if self == SearchToolEnum.GREP:
            return "Grep: use command to match the keywords in code snippet, \
            the query input should be specific keywords and searchable, like function, class, variables, api name, etc。"

        elif self == SearchToolEnum.EMBEDDING:
            return "EMBEDDING: use vector database to search code \
            the query input should be general and cover all possible answers"

        elif self == SearchToolEnum.TERM_SPRSE:
            return "TERM_SPRSE: use TERM_SPRSE algorithm to search code, \
            the query input could be the Pseudo-code code snippet that associated with the query and likely to appear in the repository"
        
        elif self == SearchToolEnum.INVERTED_INDEX:
            return "INVERTED_INDEX: use inverted index to search code, \
            the query input could be the Pseudo-code code snippet that associated with the query and likely to appear in the repository"
        
        elif self == SearchToolEnum.ANY:
            return "ANY: use all appropriate search tool to get the required information, tools include `grep`, `inverted_index`, and `term_sparse` \
            the query should specify the name of the tool to be used and the input for the corresponding tool, `grep` and `inverted_index`are suitable for searching specific keywords and sentence, \
            `term_sparse` is suitable for searching pseudo-code code snippet"
        else:
            return "未知搜索工具"

    @property
    def examples(self):
        if self == SearchToolEnum.GREP:
            return f"""
Original Query: "解释这个存储级别的仓库的主要功能"
Output:
<output tool="grep">
<{NEW_QUERY_TAG}>Repository</{NEW_QUERY_TAG}>
<{NEW_QUERY_TAG}>Storage</{NEW_QUERY_TAG}>
<{NEW_QUERY_TAG}>Persist</{NEW_QUERY_TAG}>
</output>
"""
        elif self == SearchToolEnum.EMBEDDING:
            return f"""
Original Query: "解释这个存储级别的仓库的主要功能"
Output:
<output tool="embedding">
<{NEW_QUERY_TAG}>Repository</{NEW_QUERY_TAG}>
<{NEW_QUERY_TAG}>Storage</{NEW_QUERY_TAG}>
<{NEW_QUERY_TAG}>Persist</{NEW_QUERY_TAG}>
</output>
"""
        elif self == SearchToolEnum.INVERTED_INDEX:
            return f"""
Original Query: "请为PyMySQL的核心API接口编写详细的文档，包括Connection类和Cursor类的所有方法、参数说明和返回值"
Output: 
<output tool="inverted_index">
{CODE_SNIPPET_EXAMPLES}
</output>
"""
        elif self == SearchToolEnum.TERM_SPRSE:
            return f"""
Original Query: "请为PyMySQL的核心API接口编写详细的文档，包括Connection类和Cursor类的所有方法、参数说明和返回值"
Output: 
<output tool="term_sparse">
{CODE_SNIPPET_EXAMPLES}
</output>
"""
        elif self == SearchToolEnum.ANY:
            return f"""
Original Query: "解释这个存储级别的仓库的主要功能"
Output:
<output tool="grep">
<{NEW_QUERY_TAG}>Repository</{NEW_QUERY_TAG}>
<{NEW_QUERY_TAG}>Storage</{NEW_QUERY_TAG}>
<{NEW_QUERY_TAG}>Persist</{NEW_QUERY_TAG}>
</output>

Original Query: "解释这个存储级别的仓库的主要功能"
Output:
<output tool="term_sparse">
{CODE_SNIPPET_EXAMPLES}
</output>
"""
        else:
            return "未知搜索工具"
        