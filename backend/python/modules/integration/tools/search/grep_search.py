import os
import re
from typing import List, Tuple
import subprocess

from modules.common.schema import CodeSnippet
from modules.integration.tools.search.search_tool import SearchToolABC
from core.config import get_config
from utils.logger import logger
from utils.file import should_ignore_path

class GrepSearchTool(SearchToolABC):
    """基于grep的搜索引擎"""
    def __init__(self, repo_path: str):
        self.repo_path = repo_path
        self.file_filter = get_config().file_filter

    def search(self, query: str) -> List[CodeSnippet]:
        """
        使用grep搜索代码片段
        
        Args:
            query: 查询字符串
            
        Returns:
            List[CodeSnippet]: 搜索结果
        """
        snippets = []
        
        try:
            # 构建grep命令
            cmd = [
                "grep",
                "-r",  # 递归搜索
                "-n",  # 显示行号
                "-i",  # 忽略大小写
                "-C", "3",  # 显示上下文3行
                query,
                self.repo_path
            ]
            
            # 添加文件类型过滤
            for ext in self.file_filter.include:
                cmd.extend(["--include", f"*{ext}"])
            
            # 执行搜索
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )

            if result.returncode == 0:
                snippets = self._parse_grep_output(result.stdout)
            
        except Exception as e:
            logger.error(f"Grep Failed: {e}")
        
        return snippets
    
    def _parse_grep_output(self, output: str) -> List[CodeSnippet]:
        """
        解析grep输出结果
        
        Args:
            output: grep命令输出
            
        Returns:
            List[CodeSnippet]: 解析后的代码片段
        """
        snippets = []
        current_file = None
        current_lines = []
        
        for line in output.split('\n'):
            if not line.strip():
                if current_file and current_lines:
                    snippets.extend(self._extract_snippets_from_lines(current_file, current_lines))
                    current_lines = []
                current_file = None
                continue
            
            # 解析grep输出格式：文件名:行号:内容 或 文件名-行号-内容（上下文）
            match = re.match(r'^([^:]+):(\d+):(.*)$', line)
            context_match = re.match(r'^([^:]+)-(\d+)-(.*)$', line)
            
            if match:
                file_path, line_number, content = match.groups()
                if not should_ignore_path(file_path):
                    current_file = file_path
                    current_lines.append((int(line_number), content, True))  # True表示匹配行
            elif context_match:
                file_path, line_number, content = context_match.groups()
                if current_file == file_path:
                    current_lines.append((int(line_number), content, False))  # False表示上下文行
        
        # 处理最后一组
        if current_file and current_lines:
            snippets.extend(self._extract_snippets_from_lines(current_file, current_lines))
        
        return snippets
    
    def _extract_snippets_from_lines(self, file_path: str, lines: List[Tuple[int, str, bool]]) -> List[CodeSnippet]:
        """
        从行数据中提取代码片段
        
        Args:
            file_path: 文件路径
            lines: 行数据列表 (行号, 内容, 是否匹配行)
            
        Returns:
            List[CodeSnippet]: 代码片段列表
        """
        snippets = []
        
        # 按匹配行分组
        i = 0
        while i < len(lines):
            line_number, content, is_match = lines[i]
            if is_match:
                # 收集上下文
                context_before = []
                context_after = []
                
                # 向前查找上下文
                j = i - 1
                while j >= 0 and not lines[j][2]:  # 上下文行
                    context_before.insert(0, lines[j][1])
                    j -= 1
                
                # 向后查找上下文
                j = i + 1
                while j < len(lines) and not lines[j][2]:  # 上下文行
                    context_after.append(lines[j][1])
                    j += 1
                
                snippet = CodeSnippet(
                    file_path=os.path.relpath(self.repo_path, file_path),
                    start_line=line_number - 1,
                    end_line=line_number - 1,
                    content=content,
                    context_before="\n".join(context_before),
                    context_after="\n".join(context_after)
                )
                snippets.append(snippet)
            
            i += 1
        
        return snippets

