"""
DeepSearch核心模块
实现完整的深度搜索流程
"""
import re
from typing import List, Dict, Optional, Tuple
import json
import time
import asyncio
from pathlib import Path

from core.config import get_config
from modules.integration.tools.search.any_search import get_search_tool
from modules.llm.llm_client import LLMClient, default_llm_client
from modules.common.schema import SearchResult, CodeSnippet
from modules.integration.tools.search.search_tool import SearchToolEnum
from modules.deepsearch.prompts import (
    SUBQUERY_FILTER_PROMPT, 
    GENERATE_NEW_QUERY_PROMPT,
    SYSTEM_PROMPTS,
    QUERY_SPLIT_PROMPT
)
from utils.logger import logger
from utils.trace_logger import get_trace_logger
from utils.trace_context import TraceAwareThreadPoolExecutor
from utils.file import build_file_tree, FileNode
from modules.common.constant import SUB_QUERY_TAG, NEW_QUERY_TAG


class DeepSearch:
    """深度搜索主类"""
    
    def __init__(
        self,
        repo_path: str,
        repo_info: str = "",
        llm_client: LLMClient = None,
        search_tool: SearchToolEnum = SearchToolEnum.ANY
    ):
        """
        初始化DeepSearch

        Args:
            repo_path: 仓库路径
            repo_info: 仓库信息描述
            llm_client: LLM客户端，如果为None则使用默认客户端
            search_type: 搜索类型
        """
        self.repo_path = repo_path
        self.repo_info = repo_info
        self.search_tool = search_tool

        self.llm_client = llm_client or default_llm_client
        self.search_manager = get_search_tool(search_tool, repo_path)
        self.config = get_config().deepsearch

        # 获取支持 trace 的 logger
        self.logger = get_trace_logger(__name__)
    
    def search(self, query: str) -> SearchResult:
        """
        执行深度搜索
        
        Args:
            query: 用户查询
            
        Returns:
            SearchResult: 搜索结果
        """
        result = SearchResult(original_query=query)
        
        self.logger.info(f"Start DeepSearch for Query: {query}")
        
        # 生成新queries
        
        # 获取当前仓库树结构
        repo_node = FileNode(path=self.repo_path, name=self.repo_path.split("/")[-1], type="directory", children=build_file_tree(Path(self.repo_path), max_depth=3))
        repo_struct = str(repo_node)

        # 生成新查询
        try:
            new_queries = self._split_query(
                query,
                repo_struct,
                self.config.max_sub_queries
            )
        except Exception as e:
            self.logger.error(f"生成新查询失败: {e}")
            return result

        # 将首次拆分的子查询的查询结果添加到集合中
        new_snippets = self._search_and_filter(new_queries, query, SearchToolEnum.TERM_SPRSE)
        result.code_snippets.extend(new_snippets)

        # 针对每个query并行查询
        for iteration in range(self.config.max_iterations):
            if not new_queries:
                logger.info("未生成新查询，搜索结束")
                break
            
            # 生成子查询
            search_tool, new_queries = self._generate_new_queries(query, repo_struct, result.all_queries, result.code_snippets)
            result.all_queries.extend(new_queries)

            # 针对每个新查询并发执行查询
            try:
                new_snippets = self._search_and_filter(new_queries, query, search_tool)
            except Exception as e:
                logger.error(f"Iteration {iteration + 1}: Search Failed: {e}")
                break
                
            result.code_snippets.extend(new_snippets)
            if not new_snippets:
                # DeepSearch的方法是预设首先拆分的搜索方向没有错误，如果子查询没有找到相关代码，说明当前搜索方向已经穷尽，可以结束搜索
                logger.warning(f"Iteration {iteration + 1}: Not Found Any Code Snippets")
                break
            
            result.iterations = iteration + 1

        # 4. 文件级别合并和去重
        logger.info("Merging Code Snippets at File Level...")
        result.code_snippets = self._deduplicate_snippets(result.code_snippets)
        
        logger.info(f"DeepSearch Completed: {result.get_summary()}")
        
        return result
    
    async def search_stream(self, query: str):
        """流式搜索"""
        """
        执行深度搜索
        
        Args:
            query: 用户查询
            
        Returns:
            SearchResult: 搜索结果
        """
        try:
            result = SearchResult(original_query=query)
            repo_node = FileNode(path=self.repo_path, name=self.repo_path.split("/")[-1], type="directory", children=build_file_tree(Path(self.repo_path), max_depth=3))
            repo_struct = str(repo_node)

            logger.info(f"Start DeepSearch for Query: {query}")
            yield f'data: {json.dumps({"type": "start", "message": "开始搜索", "timestamp": time.time()})}\n\n'
            await asyncio.sleep(0.01)  # 确保数据被及时发送

            for iteration in range(self.config.max_iterations):
                result.iterations = iteration + 1
                yield f'data: {json.dumps({"type": "process", "message": f"开始第{iteration + 1}次迭代", "timestamp": time.time()})}\n\n'
                await asyncio.sleep(0.01)

                new_queries = []
                
                try:
                    new_queries = self._generate_new_queries(
                        query,
                        repo_struct,
                        result.all_queries,
                        result.code_snippets
                    )
                except Exception as e:
                    logger.error(f"生成新查询失败: {e}")
                    yield f'data: {json.dumps({"type": "error", "message": f"第{iteration + 1}次搜索: 生成新查询失败: {e}", "timestamp": time.time()})}\n\n'
                    break

                logger.info(f"Iteration {iteration + 1}: {query}")
                logger.info(f"Iteration {iteration + 1}: Generated {len(new_queries)} New Queries: {new_queries}")
                if len(new_queries) == 0:
                    yield f'data: {json.dumps({"type": "complete", "message": f"第{iteration + 1}次搜索: 未生成新查询，搜索结束", "timestamp": time.time()})}\n\n'
                    break

                new_queries_str = '\n'.join([f"{i+1}. {q}" for i, q in enumerate(new_queries)])
                yield f'data: {json.dumps({"type": "process", "message": f"第{iteration + 1}次搜索查询列表:\n" + new_queries_str, "timestamp": time.time()})}\n\n'
                await asyncio.sleep(0.01)

                result.all_queries.extend(new_queries)

                # 搜索新查询
                yield f'data: {json.dumps({"type": "process", "message": f"第{iteration + 1}次搜索: 开始执行搜索...", "timestamp": time.time()})}\n\n'
                try:
                    new_snippets = self._search_and_filter(new_queries, query)
                    yield f'data: {json.dumps({"type": "process", "message": f"第{iteration + 1}次搜索: 搜索完成，找到 {len(new_snippets)} 个代码片段", "timestamp": time.time()})}\n\n'
                except Exception as e:
                    logger.error(f"搜索失败: {e}")
                    yield f'data: {json.dumps({"type": "error", "message": f"第{iteration + 1}次搜索: 搜索失败: {e}", "timestamp": time.time()})}\n\n'
                    break
                
                result.code_snippets.extend(new_snippets)
                await asyncio.sleep(0.01)
            
            # 4. 文件级别合并和去重
            logger.info("Merging Code Snippets at File Level...")
            yield f'data: {json.dumps({"type": "process", "message": "合并代码片段...", "timestamp": time.time()})}\n\n'
            result.code_snippets = self._deduplicate_snippets(result.code_snippets)
            
            # 5. 展示最终搜索结果
            total_snippets = len(result.code_snippets)
            total_files = len(set(snippet.file_path for snippet in result.code_snippets))
            
            logger.info(f"搜索结果统计: 共找到 {total_snippets} 个代码片段，涉及 {total_files} 个文件")
            yield f'data: {json.dumps({"type": "process", "message": f"📊 搜索结果统计: 共找到 {total_snippets} 个代码片段，涉及 {total_files} 个文件", "timestamp": time.time()})}\n\n'
            await asyncio.sleep(0.01)
            
            if total_snippets > 0:
                # 按文件分组展示结果
                file_groups = {}
                for snippet in result.code_snippets:
                    if snippet.file_path not in file_groups:
                        file_groups[snippet.file_path] = []
                    file_groups[snippet.file_path].append(snippet)
                
                yield f'data: {json.dumps({"type": "process", "message": "🔍 详细搜索结果:", "timestamp": time.time()})}\n\n'
                await asyncio.sleep(0.01)
                
                for file_path, snippets in file_groups.items():
                    yield f'data: {json.dumps({"type": "process", "message": f"📁 {file_path} ({len(snippets)} 个片段)", "timestamp": time.time()})}\n\n'
                    
                    for i, snippet in enumerate(snippets, 1):
                        snippet_info = f"  {i}. 行 {snippet.start_line}-{snippet.end_line}"
                        # 尝试从内容中提取函数名
                        lines = snippet.content.split('\n')
                        for line in lines[:3]:  # 只检查前3行
                            line = line.strip()
                            if line.startswith('def ') or line.startswith('class ') or line.startswith('function '):
                                func_match = line.split('(')[0].split(' ', 1)
                                if len(func_match) > 1:
                                    snippet_info += f" ({func_match[0]}: {func_match[1]})"
                                break
                        yield f'data: {json.dumps({"type": "process", "message": snippet_info, "timestamp": time.time()})}\n\n'
                    
                    await asyncio.sleep(0.01)
            else:
                yield f'data: {json.dumps({"type": "process", "message": "⚠️ 未找到相关代码片段", "timestamp": time.time()})}\n\n'
            
            logger.info(f"DeepSearch Completed: {result.get_summary()}")
            yield f'data: {json.dumps({"type": "process", "message": f"✅ 搜索完成: {result.get_summary()}", "timestamp": time.time()})}\n\n'

            # 发送完成事件
            yield f'data: {json.dumps({"type": "complete", "message": "搜索完成", "timestamp": time.time()})}\n\n'
            await asyncio.sleep(0.01)  # 确保完成事件被及时发送

        except Exception as e:
            logger.error(f"DeepSearch Failed: {e}")
            yield f'data: {json.dumps({"type": "error", "message": f"搜索失败: {e}", "timestamp": time.time()})}\n\n'
    
    def _split_query(self, query: str, repo_struct: str, max_sub_queries: int = 3) -> List[str]:
        prompt = QUERY_SPLIT_PROMPT.format(
            original_query=query,
            max_subquries=max_sub_queries,
            repo_struct = repo_struct,
            SUB_QUERY_TAG=SUB_QUERY_TAG
        )
        
        response_text = self.llm_client.call(
            prompt, 
            QUERY_SPLIT_PROMPT, 
            stream=False
        )
        _, sub_queries = self._extract_structure(
            response_text,
            structure_keyword=SUB_QUERY_TAG
        )

        # 限制子查询数量
        return sub_queries[:max_sub_queries]
        

    def _search_and_filter(self, queries: List[str], original_query: str, search_tool: SearchToolEnum = None) -> List[CodeSnippet]:
        """
        搜索并过滤代码片段
        
        Args:
            queries: 查询列表
            original_query: 原始查询（用于过滤）
            
        Returns:
            List[CodeSnippet]: 过滤后的代码片段
        """
        # 并行检索所有子查询
        logger.info(f"Searching Tool: {search_tool.name}")
        logger.info(f"Searching Sub-Queries...: {queries}")

        search_results = {}
        
        # 使用线程池进行并发搜索（避免多进程的序列化问题）
        import concurrent.futures
        max_workers = min(len(queries), 4)  # 限制线程数
        
        if max_workers == 0:
            return []

        with TraceAwareThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有搜索任务
            future_to_query = {
                executor.submit(self.search_manager.search, query=query, search_tool=search_tool): query for query in queries
            }
            
            # 收集搜索结果
            for future in concurrent.futures.as_completed(future_to_query):
                query = future_to_query[future]
                try:
                    snippets = future.result()
                    logger.info(f"Query '{query}' Found {len(snippets)} Code Snippets")
                    search_results[query] = snippets
                except Exception as exc:
                    logger.info(f"Query '{query}' Search Failed: {exc}")
                    search_results[query] = []

        # 统计总的代码片段数量
        all_snippets = [snippet for snippets in search_results.values() for snippet in snippets]
        logger.info(f"Found {len(all_snippets)} Code Snippets, Start Filtering...")

        # TODO: 过滤

        # 去重
        unique_snippets = self._deduplicate_snippets(all_snippets)
        logger.info(f"Filtered Snippets Deduplicated to {len(unique_snippets)} Unique Snippets")
        
        return unique_snippets

    def _filter_snippets_for_query(self, snippets: List[CodeSnippet], sub_query: str, original_query: str) -> List[CodeSnippet]:
        """
        为特定子查询过滤代码片段
        
        Args:
            snippets: 代码片段列表
            sub_query: 子查询
            original_query: 原始查询
            
        Returns:
            List[CodeSnippet]: 过滤后的代码片段
        """
        filtered_snippets = []
        
        # 使用子查询和原始查询的组合来判断相关性
        # combined_query = f"Original Query: {original_query}\\nSub Query: {sub_query}"
        combined_query = original_query
        for snippet in snippets:
            if self._is_relevant_snippet(snippet, combined_query):
                filtered_snippets.append(snippet)
        
        return filtered_snippets
    
    def _deduplicate_snippets(self, snippets: List[CodeSnippet]) -> List[CodeSnippet]:
        """
        去重代码片段（基于文件路径和行号）
        
        Args:
            snippets: 代码片段列表
            
        Returns:
            List[CodeSnippet]: 去重后的代码片段列表
        """
        seen_stack = []
        unique_snippets = []
        
        sorted_snippets = sorted(snippets, key=lambda x: (x.file_path, x.start_line, x.end_line))

        for snippet in sorted_snippets:
            # 使用文件路径和行号作为唯一标识
            key = (snippet.file_path, snippet.start_line, snippet.end_line)
            
            if seen_stack and seen_stack[-1][0] == key[0] and seen_stack[-1][1] <= key[1] <= seen_stack[-1][2]:
                # 更新seen_stack的end_line
                seen_stack[-1][2] = max(seen_stack[-1][2], key[2])
                
                # 如果end_line在前一个snippet后面，则合并snippet内容
                if unique_snippets[-1].end_line >= key[2]:
                    unique_snippets[-1].content += "\n".join(snippet.content.split('\n')[(unique_snippets[-1].end_line - snippet.start_line + 1):])
                    unique_snippets[-1].score += snippet.score # 合并后的score相加

                # 更新end_line
                unique_snippets[-1].end_line = seen_stack[-1][2]
                    
            else:   
                seen_stack.append(list(key)) # 因为元素可能被修改，tuple是不可变元组，因此使用list存入
                unique_snippets.append(snippet)

        return unique_snippets
    
    def _is_relevant_snippet(self, snippet: CodeSnippet, query: str) -> bool:
        """
        判断代码片段是否与查询相关
        
        Args:
            snippet: 代码片段
            query: 查询字符串
            
        Returns:
            bool: 是否相关
        """
        prompt = SUBQUERY_FILTER_PROMPT.format(
            repository_info=self.repo_info,
            query=query,
            code_snippet=snippet.get_full_content()
        )
        logger.info(f"Filtering prompt: {prompt}")

        response = self.llm_client.call(prompt, SYSTEM_PROMPTS["filter"])
        logger.info(f"Filtering response: {response}")
        return response.strip().upper() == "YES"
    
    def _generate_new_queries(
        self, 
        original_query: str, 
        repo_struct: str,
        previous_queries: List[str], 
        code_snippets: List[CodeSnippet]
    ) -> Tuple[SearchToolEnum, List[str]]:
        """
        生成新的查询
        
        Args:
            original_query: 原始查询
            repo_struct: 仓库结构
            previous_queries: 之前的查询列表
            code_snippets: 已找到的代码片段
            
        Returns:
            List[str]: 新查询列表
        """
        # 构建代码片段摘要

        prompt = GENERATE_NEW_QUERY_PROMPT.format(
            question=original_query,
            repo_struct=repo_struct,
            previous_queries=previous_queries,
            max_new_queries=self.config.max_new_queries,
            NEW_QUERY_TAG=NEW_QUERY_TAG,
            code_snippets=self._build_code_summary(code_snippets),
            tool_description=self.search_tool.description,
            tool_exampls=self.search_tool.examples
        )
        
        respose_text = self.llm_client.call(prompt, prompt, stream=False)

        search_tool, new_queries = self._extract_structure(
            respose_text,
            structure_keyword=NEW_QUERY_TAG
        )

        # 如果当前的搜索工具不是any，则优先使用指定的搜索工具
        if self.search_tool != SearchToolEnum.ANY:
            search_tool = self.search_tool

        # 限制新查询数量
        return search_tool, new_queries[:self.config.max_new_queries]
    
    def _build_code_summary(self, snippets: List[CodeSnippet]) -> str:
        """
        构建代码片段摘要
        
        Args:
            snippets: 代码片段列表
            
        Returns:
            str: 代码摘要
        """
        if not snippets:
            return "暂无相关代码片段"
        
        # 按文件分组
        file_groups = {}
        for snippet in snippets:
            if snippet.file_path not in file_groups:
                file_groups[snippet.file_path] = []
            file_groups[snippet.file_path].append(snippet)
        
        # 构建摘要
        summary_parts = []
        for file_path, file_snippets in file_groups.items():
            summary_parts.append(f"\n文件: {file_path}")
            for snippet in file_snippets[:3]:  # 每个文件最多显示3个片段
                summary_parts.append(f"  行 {snippet.start_line}-{snippet.end_line}: {snippet.content[:100]}...")
        
        return "\n".join(summary_parts)

    def _extract_structure(self, response_text: str, structure_keyword: str = None) -> Tuple[Optional[SearchToolEnum], List[str]]:
        """
        从LLM回复中提取元信息和结构化内容

        Args:
            response_text: 待提取的文本
            structure_keyword: 结构化关键字

        Raises:
            ValueError: 如果结构化关键字为空

        Notes:
            1. 提取<output tool="xxx">中的搜索工具类型信息
            2. 结构化关键字用于标识回复的开始和结束
            3. 回复内容在结构化关键字之间
            4. 结构化关键字可以是任意字符串，如<subquery>和</subquery>
            5. 回复内容可以包含多个结构化关键字

        Returns:
            Tuple[Dict[str, str], List[str]]: 包含元信息字典和结构化内容列表的元组
        """
        if not structure_keyword:
            raise ValueError("结构化关键字不能为空")

        # 提取结构化内容
        # 提取<output>中的元信息，支持多个键值对，如<output tool="term_sparse" timeout="30" max_results="10">
        meta_info = {}

        # 使用正则表达式匹配<output>标签及其所有属性
        output_match = re.search(r'<output\s+([^>]+)>', response_text)
        if output_match:
            attributes_str = output_match.group(1)

            # 解析所有的key="value"格式的属性
            # 支持单引号和双引号
            attr_pattern = r'(\w+)=(["\'])([^"\']*?)\2'
            attr_matches = re.findall(attr_pattern, attributes_str)

            for attr_name, _, attr_value in attr_matches:
                meta_info[attr_name.strip()] = attr_value.strip()

            logger.debug(f"提取到的元信息: {meta_info}")

        # 构建开始和结束标签
        start_tag = f"<{structure_keyword}>"
        end_tag = f"</{structure_keyword}>"

        # 提取所有结构化内容
        structured_contents = []

        # 使用正则表达式查找所有匹配的结构化内容
        pattern = f"{re.escape(start_tag)}(.*?){re.escape(end_tag)}"
        matches = re.findall(pattern, response_text, re.DOTALL)

        for match in matches:
            # 清理内容：去除首尾空白字符，并按行分割
            structured_contents.append(match.strip())
        
        logger.debug(f"原始回复: {response_text}, 元数据: {meta_info}, 提取到的结构化内容: {structured_contents}")

        search_tool = SearchToolEnum.TERM_SPRSE
        if "tool" in meta_info and meta_info["tool"].lower() in SearchToolEnum._member_names_:
            search_tool = SearchToolEnum(meta_info["tool"].lower())

        # 返回元信息字典和结构化内容列表
        return search_tool, structured_contents

    def _get_search_tool_from_meta(self, meta_info: Dict[str, str]) -> SearchToolEnum:
        """
        从元信息字典中获取搜索工具类型

        Args:
            meta_info: 元信息字典

        Returns:
            SearchToolEnum: 搜索工具类型
        """
        tool_name = meta_info.get("tool", "any").lower()

        try:
            if tool_name == "grep":
                return SearchToolEnum.GREP
            elif tool_name == "embedding":
                return SearchToolEnum.EMBEDDING
            elif tool_name == "inverted_index":
                return SearchToolEnum.INVERTED_INDEX
            elif tool_name == "term_sparse":
                return SearchToolEnum.TERM_SPRSE
            elif tool_name == "any":
                return SearchToolEnum.ANY
            else:
                logger.warning(f"未知的搜索工具类型: {tool_name}，使用默认工具")
                return SearchToolEnum.ANY
        except Exception as e:
            logger.warning(f"解析搜索工具类型失败: {e}，使用默认工具")
            return SearchToolEnum.ANY