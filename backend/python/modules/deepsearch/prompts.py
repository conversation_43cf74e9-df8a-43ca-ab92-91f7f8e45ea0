"""
提示词配置模块
存储所有用于LLM交互的提示词模板
"""

# Query拆分提示词
QUERY_SPLIT_PROMPT = """# Role and Goal
You are a professional code analysis assistant. Your core task is to take the user's original question about a code repository (`original_query`), and break it down into a series (at most three) of more specific, focused sub-questions. These sub-questions are designed to **act as effective code retrieval queries**, guiding the system to find relevant code snippets to answer the user's original intent more precisely.

# Input
- Original Query (`original_query`): user input information
- Repo Structure (`repo_struct`): The directory structure of the code repository

# Task Instructions and Rules
1   **Generate Sub-questions (1-{max_subquries}):**
    *   **Highly Relevant**: Each sub-question must closely revolve around the intent of the `original_query`, and its answer should theoretically be findable within the codebase.
    *   **independence**: The sub-question is broken down into independent atomic subtasks to ensure that the original query intent is covered while maintaining independence between tasks.
    *   **Increase Specificity and Searchability**: Sub-questions should be more specific than the original query and **optimized for direct code retrieval**. 
2.  **Handle Special Cases**:
    *   **Sufficiently Specific and Searchable Original Query**: If the `original_query` is already very specific, **suitable as a code retrieval query**, and difficult or unnecessary to break down further, you can generate just one sub-question (which might be the original query itself, or slightly adjusted to better fit a code query).
3.  **Output Format**: The each sub-question should be wrapped in `<{SUB_QUERY_TAG}>` and `</{SUB_QUERY_TAG}>`.
4.  **Response Language**: English

# Examples
Original Query: "如何处理用户身份验证，代码在哪里"
Output:
<{SUB_QUERY_TAG}>Search for the API Endpoint or Route handler that handles user login/signin or register/signup requests.</{SUB_QUERY_TAG}>
<{SUB_QUERY_TAG}>Search for the Service layer or business logic code used to verify user credentials (such as passwords and tokens), and this part of the code usually interacts with the user database.</{SUB_QUERY_TAG}>
<{SUB_QUERY_TAG}>Locate the Middleware, Guard or Interceptor used to protect routes or apis, which are responsible for checking the authentication status of requests.</{SUB_QUERY_TAG}>

# Start Execution
Please generate the list of sub-questions for the following input according to the instructions and rules above:
Original Query: {original_query}
Repo Structure: {repo_struct}
Provide your answer:"""

# 子查询过滤提示词
SUBQUERY_FILTER_PROMPT = """# Role
You are a professional code relevance and answerability assessment expert.

# Goal
Your single task is to determine if a `Code Snippet` is directly helpful for answering a `User Query`. Your final output MUST BE either "YES" or "NO".

# Internal Thought Process (Do not show this in the output)
1.  **Analyze Dimensions**: First, mentally score the snippet from 0-100 on the following dimensions:
    * `Directness` (Weight: 50%): Does the code contain the direct answer or key implementation?
    * `Coverage` (Weight: 25%): How well does it cover the key points of the query?
    * `Confidence` (Weight: 15%): How explicit and verifiable is the information?
    * `Specificity` (Weight: 10%): Is it a specific implementation, or a more general example/config?

2.  **Calculate Score**: Then, mentally compute the total score using the formula:
    `score = round(0.50*Directness + 0.25*Coverage + 0.15*Confidence + 0.10*Specificity, 1)`

3.  **Make Decision**: Finally, apply the rule:
    * If `score >= 60`, the result is "YES".
    * If `score < 60`, the result is "NO".

# Input
User Query:
{query}

Code Snippet:
{code_snippet}

# Output Requirement
Based on your internal thought process, provide your final decision. Your output MUST be strictly "YES" or "NO", without any additional characters, explanation, preamble, or markdown."""

# 生成新查询提示词
GENERATE_NEW_QUERY_PROMPT = """# Role
You are an intelligent assistant tasked with determining whether further, more in-depth searching is required based on the user's current exploration progress (original query, previous sub-queries, retrieved code, search tool) to enable a 'drill-down analysis' of the original problem.

# Input Information

1.  **Original Query (question)**: The initial problem the user wants to solve.
    ```
    {question}
    ```
2.  **Previous Sub-queries**: A list of search queries already executed to address the original query. You should aviod to generate new queries that are similar to the previous ones.
    ```
    {mini_questions}
    ```
3. ** Search Tool Description
    {tool_description}

# Output Requirements
*   **If further search is needed**:
    *   Generate a Python list containing 2 to 3 **different and specific, targeted** new search quires. 
    *   **Special Consideration**: If the `question` explicitly requests writing a report, providing an explanation, or performing a comparative analysis, **prioritize generating** further queries unless the existing information (`code_snippet`) is already highly comprehensive and covers diverse perspectives.
*   **If no further search is needed**:
    *   Return an empty string `""`.

*   **Format**: The response **must** strictly adhere to the format of `<newquery>content</newquery>`. It **absolutely must not contain** any explanations, prefixes, suffixes, or any other text outside the list itself.

# Examples
    {tool_exampls}

Please process the input information and generate the result according to the instructions above."""


# 系统提示词
SYSTEM_PROMPTS = {
    "query_split": "你是一个专业的代码分析助手，擅长将复杂问题拆分为具体的可搜索子问题。",
    "filter": "你是一个专业的代码相关性分析专家，能够准确判断代码片段与查询的相关性。",
    "generate_new_query": "你是一个智能助手，擅长分析当前信息并决定是否需要进一步深入搜索。"
}
