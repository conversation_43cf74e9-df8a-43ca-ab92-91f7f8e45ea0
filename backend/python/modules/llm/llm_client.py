"""
LLM客户端模块
提供统一的大模型调用接口
"""
import httpx
import json
from typing import Union, Dict, List, AsyncGenerator

from core.config import get_config
from utils.trace_logger import trace_logger

class LLMClient:
    """LLM调用客户端"""
    
    def __init__(self):
        """
        初始化LLM客户端
        """
        self.config = get_config().llm
    
    def _get_headers(self) -> Dict:
        return {
            "Authorization": f"{self.config.api_key}",
            "Content-Type": "application/json"
        }
    
    def _get_request(self, messages: List[Dict], stream: bool = False) -> Dict:
        return {
            "model": self.config.model,
            "messages": messages,
            "stream": stream,
            "temperature": self.config.temperature,
            "max_tokens": self.config.max_tokens
        }
    
    async def completion(self, messages: List[Dict[str, str]], stream: bool = False) -> Union[str, AsyncGenerator[str, None]]:
        request_data = self._get_request(messages, stream)
        headers = self._get_headers()


        if stream:
            return self._stream_completion(request_data, headers)
        else:
            return await self._non_stream_completion(request_data, headers)

    async def _stream_completion(self, request_data: dict, headers: dict) -> AsyncGenerator[str, None]:
        """处理流式响应"""
        async with httpx.AsyncClient() as client:
            async with client.stream("POST", self.config.base_url, json=request_data, headers=headers) as response:
                # 检查响应状态
                if response.status_code != 200:
                    trace_logger.error(f"LLM流式调用失败，状态码: {response.status_code}")
                    return

                # 逐行读取流式响应
                async for line in response.aiter_lines():
                    if not line.strip():
                        continue

                    # 处理SSE格式的数据
                    if line.startswith("data: "):
                        data_content = line[6:]  # 移除 "data: " 前缀

                        # 检查是否为结束标志
                        if data_content.strip() == "[DONE]":
                            break

                        try:
                            # 解析JSON数据
                            chunk_data = json.loads(data_content)

                            # 提取内容
                            if 'choices' in chunk_data and len(chunk_data['choices']) > 0:
                                choice = chunk_data['choices'][0]
                                if 'delta' in choice and 'content' in choice['delta']:
                                    chunk_content = choice['delta']['content']
                                    if chunk_content:
                                        yield chunk_content

                        except json.JSONDecodeError as e:
                            trace_logger.warning(f"解析流式响应JSON失败: {e}, 数据: {data_content}")
                            continue
                        except Exception as e:
                            trace_logger.warning(f"处理流式响应块失败: {e}")
                            continue

    async def _non_stream_completion(self, request_data: dict, headers: dict) -> str:
        """处理非流式响应"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.config.base_url,
                    json=request_data,
                    headers=headers,
                    timeout=self.config.timeout
                )

                # logger.info(f"LLM调用响应状态码: {response.status_code}")

                if response.status_code != 200:
                    trace_logger.error(f"LLM调用失败，状态码: {response.status_code}, 响应: {response.text}")
                    return ""

                result = response.json()

                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    return content
                else:
                    trace_logger.warning(f"响应中没有choices字段: {result}")
                    return ""

        except httpx.TimeoutException:
            trace_logger.error("LLM调用超时")
            return ""
        except json.JSONDecodeError as e:
            trace_logger.error(f"解析响应JSON失败: {e}")
            return ""
        except Exception as e:
            trace_logger.error(f"LLM调用失败: {e}")
            return ""
        
    def call(self, prompt: str, system_prompt: str = "你是一个专业的代码分析助手", stream: bool = False) -> str:
        """
        调用LLM生成回复（同步方法）

        Args:
            prompt: 用户提示词
            system_prompt: 系统提示词
            stream: 是否使用流式响应

        Returns:
            str: LLM生成的回复
        """
        import asyncio
        
        trace_logger.info(f"LLM调用请求: {prompt}")

        def run_in_thread():
            """在新线程中运行异步代码"""
            async def _async_call():
                """内部异步调用方法"""
                response = await self.completion(
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": prompt}
                    ],
                    stream=stream
                )

                if stream:
                    # 如果是流式响应，需要收集所有块
                    response_text = ""
                    async for chunk in response:
                        response_text += chunk
                    return response_text
                else:
                    # 非流式响应直接返回字符串
                    return response

            # 在新的事件循环中运行
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(_async_call())
                return result
            finally:
                loop.close()

        try:
            # 尝试在当前线程运行
            try:
                asyncio.get_running_loop()
                # 如果已经有运行中的事件循环，使用线程
                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_in_thread)
                    return future.result()
            except RuntimeError:
                # 没有运行中的事件循环，直接运行
                return run_in_thread()

        except Exception as e:
            trace_logger.error(f"LLM调用失败: {e}")
            return ""

# 创建默认的LLM客户端实例
default_llm_client = LLMClient()
