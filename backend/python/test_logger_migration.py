#!/usr/bin/env python3
"""
测试 logger 迁移到 trace-aware logger 的功能
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.trace_context import TraceContextManager
from utils.logger import logger, get_logger, info, warning, error, critical, debug


def test_backward_compatibility():
    """测试向后兼容性"""
    print("=== 测试向后兼容性 ===")
    
    # 测试原有的导入方式仍然有效
    try:
        from utils.logger import logger, get_logger, info, warning, error, critical, debug
        print("✅ 原有导入方式正常")
    except ImportError as e:
        print(f"❌ 原有导入方式失败: {e}")
        return False
    
    # 测试便捷函数
    try:
        info("测试 info 便捷函数")
        warning("测试 warning 便捷函数")
        error("测试 error 便捷函数")
        critical("测试 critical 便捷函数")
        debug("测试 debug 便捷函数")
        print("✅ 便捷函数正常工作")
    except Exception as e:
        print(f"❌ 便捷函数失败: {e}")
        return False
    
    # 测试 logger 对象
    try:
        logger.info("测试 logger 对象")
        logger.warning("测试 logger 对象")
        logger.error("测试 logger 对象")
        print("✅ logger 对象正常工作")
    except Exception as e:
        print(f"❌ logger 对象失败: {e}")
        return False
    
    # 测试 get_logger 函数
    try:
        test_logger = get_logger("test_module")
        test_logger.info("测试命名 logger")
        print("✅ get_logger 函数正常工作")
    except Exception as e:
        print(f"❌ get_logger 函数失败: {e}")
        return False
    
    return True


def test_trace_functionality():
    """测试 trace 功能"""
    print("\n=== 测试 trace 功能 ===")
    
    # 测试没有 trace_id 的情况
    try:
        logger.info("没有 trace_id 的日志")
        print("✅ 无 trace_id 日志正常")
    except Exception as e:
        print(f"❌ 无 trace_id 日志失败: {e}")
        return False
    
    # 测试有 trace_id 的情况
    try:
        with TraceContextManager("test-trace-001", user_id=12345):
            logger.info("有 trace_id 的日志")
            logger.warning("有 trace_id 的警告")
            logger.error("有 trace_id 的错误")
        print("✅ 有 trace_id 日志正常")
    except Exception as e:
        print(f"❌ 有 trace_id 日志失败: {e}")
        return False
    
    # 测试便捷函数的 trace 功能
    try:
        with TraceContextManager("convenience-trace-001"):
            info("便捷函数 info 带 trace")
            warning("便捷函数 warning 带 trace")
            error("便捷函数 error 带 trace")
        print("✅ 便捷函数 trace 功能正常")
    except Exception as e:
        print(f"❌ 便捷函数 trace 功能失败: {e}")
        return False
    
    return True


def test_module_imports():
    """测试各个模块的导入"""
    print("\n=== 测试模块导入 ===")
    
    modules_to_test = [
        "utils.file",
        "modules.llm.llm_client", 
        "modules.integration.tools.search.grep_search",
        "modules.integration.tools.search.term_sparse_search",
        "modules.term.term"
    ]
    
    success_count = 0
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {module_name} 导入成功")
            success_count += 1
        except Exception as e:
            print(f"❌ {module_name} 导入失败: {e}")
    
    print(f"模块导入成功率: {success_count}/{len(modules_to_test)}")
    return success_count == len(modules_to_test)


def test_logger_types():
    """测试 logger 类型"""
    print("\n=== 测试 logger 类型 ===")
    
    try:
        from utils.trace_logger import TraceAwareLogger
        
        # 测试默认 logger 类型
        if hasattr(logger, 'info') and hasattr(logger, 'warning'):
            print("✅ 默认 logger 具有正确的方法")
        else:
            print("❌ 默认 logger 缺少必要方法")
            return False
        
        # 测试 get_logger 返回的类型
        test_logger = get_logger("type_test")
        if isinstance(test_logger, TraceAwareLogger):
            print("✅ get_logger 返回 TraceAwareLogger 类型")
        else:
            print(f"❌ get_logger 返回错误类型: {type(test_logger)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ logger 类型测试失败: {e}")
        return False


def test_specific_modules():
    """测试特定模块的 logger 使用"""
    print("\n=== 测试特定模块 logger 使用 ===")
    
    # 测试 utils.file 模块
    try:
        from utils.file import should_ignore_path
        # 这个函数内部使用了 logger
        result = should_ignore_path("/test/path")
        print("✅ utils.file 模块 logger 正常")
    except Exception as e:
        print(f"❌ utils.file 模块 logger 失败: {e}")
        return False
    
    # 测试 modules.llm.llm_client 模块
    try:
        from modules.llm.llm_client import LLMClient
        # 创建实例不会出错
        client = LLMClient()
        print("✅ modules.llm.llm_client 模块 logger 正常")
    except Exception as e:
        print(f"❌ modules.llm.llm_client 模块 logger 失败: {e}")
        return False
    
    return True


def test_trace_propagation():
    """测试 trace 传播"""
    print("\n=== 测试 trace 传播 ===")
    
    try:
        from utils.trace_context import get_trace_id
        
        # 测试嵌套上下文
        with TraceContextManager("outer-trace"):
            outer_id = get_trace_id()
            logger.info(f"外层 trace_id: {outer_id}")
            
            with TraceContextManager("inner-trace"):
                inner_id = get_trace_id()
                logger.info(f"内层 trace_id: {inner_id}")
                
                if inner_id == "inner-trace":
                    print("✅ 嵌套 trace 上下文正常")
                else:
                    print(f"❌ 嵌套 trace 上下文错误: {inner_id}")
                    return False
            
            # 回到外层
            back_to_outer = get_trace_id()
            if back_to_outer == outer_id:
                print("✅ trace 上下文恢复正常")
            else:
                print(f"❌ trace 上下文恢复错误: {back_to_outer}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ trace 传播测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始 logger 迁移测试...\n")
    
    tests = [
        ("向后兼容性", test_backward_compatibility),
        ("trace 功能", test_trace_functionality),
        ("模块导入", test_module_imports),
        ("logger 类型", test_logger_types),
        ("特定模块", test_specific_modules),
        ("trace 传播", test_trace_propagation),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"运行测试: {test_name}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 测试通过\n")
            else:
                print(f"❌ {test_name} 测试失败\n")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}\n")
    
    print("="*50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！logger 迁移成功！")
        return 0
    else:
        print("⚠️  部分测试失败，需要检查迁移问题")
        return 1


if __name__ == "__main__":
    exit(main())
