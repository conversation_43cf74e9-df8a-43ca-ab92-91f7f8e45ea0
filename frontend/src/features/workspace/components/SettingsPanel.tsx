import { Select } from '@/components/ui';

interface SettingsPanelProps {
  searchType: 'grep' | 'term_sparse' | 'embedding' | 'keywords';
  onSearchTypeChange: (type: 'grep' | 'term_sparse' | 'embedding' | 'keywords') => void;
}

export function SettingsPanel({ searchType, onSearchTypeChange }: SettingsPanelProps) {
  const searchTypeOptions = [
    { label: 'Grep 搜索', value: 'grep' as const },
    { label: 'Term Sparse 搜索', value: 'term_sparse' as const },
    { label: 'Embedding 搜索', value: 'embedding' as const },
    { label: '关键词搜索', value: 'keywords' as const },
  ];


  return (
    <div className="space-y-4">
      <div className="space-y-3">
        <Select
          options={searchTypeOptions}
          value={searchType}
          onChange={onSearchTypeChange}
        />
      </div>
    </div>
  );
}
